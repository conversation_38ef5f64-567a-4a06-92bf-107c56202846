<svg xmlns="http://www.w3.org/2000/svg" width="1458px" height="513px" version="1.1"><defs/><g transform="translate(0.5,0.5)"><rect x="131" y="1" width="120" height="60" rx="9" ry="9" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 31px; margin-left: 132px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Audio Input</div></div></div></foreignObject><text x="191" y="35" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="1" y="1" width="120" height="60" rx="9" ry="9" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 31px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Video Input</div></div></div></foreignObject><text x="61" y="35" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="156" y="111" width="70" height="170" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="236" y="221" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 248 221 L 248 281 M 344 221 L 344 281" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 251px; margin-left: 249px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">VAD</div></div></div></foreignObject><text x="296" y="255" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="161" y="181" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 201px; margin-left: 162px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Mic Audio</div></div></div></foreignObject><text x="191" y="205" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="161" y="231" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 251px; margin-left: 162px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Mic Audio</div></div></div></foreignObject><text x="191" y="255" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="376" y="111" width="70" height="170" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="456" y="221" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 468 221 L 468 281 M 564 221 L 564 281" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 251px; margin-left: 469px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">ASR</div></div></div></foreignObject><text x="516" y="255" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="381" y="181" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 201px; margin-left: 382px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Human Audio</div></div></div></foreignObject><text x="411" y="205" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="381" y="231" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 251px; margin-left: 382px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Human Audio</div></div></div></foreignObject><text x="411" y="255" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="596" y="111" width="70" height="170" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="676" y="221" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 688 221 L 688 281 M 784 221 L 784 281" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 251px; margin-left: 689px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">LLM</div></div></div></foreignObject><text x="736" y="255" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="601" y="181" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 201px; margin-left: 602px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Human Text</div></div></div></foreignObject><text x="631" y="205" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="601" y="231" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 251px; margin-left: 602px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Human Text</div></div></div></foreignObject><text x="631" y="255" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="816" y="111" width="70" height="170" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="896" y="221" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 908 221 L 908 281 M 1004 221 L 1004 281" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 251px; margin-left: 909px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">TTS</div></div></div></foreignObject><text x="956" y="255" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="821" y="181" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 201px; margin-left: 822px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Avatar Text</div></div></div></foreignObject><text x="851" y="205" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="821" y="231" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 251px; margin-left: 822px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Avatar Text</div></div></div></foreignObject><text x="851" y="255" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="1036" y="111" width="70" height="170" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 1236 251 L 1396 251 L 1396 304.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1396 309.88 L 1392.5 302.88 L 1396 304.63 L 1399.5 302.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1236 251 L 1256 251 L 1256 304.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1256 309.88 L 1252.5 302.88 L 1256 304.63 L 1259.5 302.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="1116" y="221" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 1128 221 L 1128 281 M 1224 221 L 1224 281" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 251px; margin-left: 1129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Avatar</div></div></div></foreignObject><text x="1176" y="255" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="1041" y="181" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 201px; margin-left: 1042px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Avatar Audio</div></div></div></foreignObject><text x="1071" y="205" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="1041" y="231" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 251px; margin-left: 1042px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Avatar Audio</div></div></div></foreignObject><text x="1071" y="255" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="376" y="311" width="70" height="200" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 576 481 L 1026 481 L 1026 161 L 1071 161 L 1071 174.63" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 1071 179.88 L 1067.5 172.88 L 1071 174.63 L 1074.5 172.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="456" y="451" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 468 451 L 468 511 M 564 451 L 564 511" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 481px; margin-left: 469px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">S2S</div></div></div></foreignObject><text x="516" y="485" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="381" y="461" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 481px; margin-left: 382px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Human Audio</div></div></div></foreignObject><text x="411" y="485" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><path d="M 191 61 L 191 71 L 191 174.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 191 179.88 L 187.5 172.88 L 191 174.63 L 194.5 172.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 356 251 L 366 251 L 366 161 L 411 161 L 411 174.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 411 179.88 L 407.5 172.88 L 411 174.63 L 414.5 172.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 576 251 L 581 251 L 581 161 L 631 161 L 631 174.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 631 179.88 L 627.5 172.88 L 631 174.63 L 634.5 172.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 356 251 L 366 251 L 366 291 L 411 291 L 411 354.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 411 359.88 L 407.5 352.88 L 411 354.63 L 414.5 352.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 796 251 L 806 251 L 806 161 L 851 161 L 851 174.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 851 179.88 L 847.5 172.88 L 851 174.63 L 854.5 172.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1016 251 L 1021 251 L 1021 131 L 1071 131 L 1071 174.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1071 179.88 L 1067.5 172.88 L 1071 174.63 L 1074.5 172.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="1196" y="311" width="120" height="60" rx="9" ry="9" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 341px; margin-left: 1197px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Audio Output</div></div></div></foreignObject><text x="1256" y="345" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="1336" y="311" width="120" height="60" rx="9" ry="9" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 341px; margin-left: 1337px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Video Output</div></div></div></foreignObject><text x="1396" y="345" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><path d="M 61 61 L 61 301 L 396 301 L 396 404.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 396 409.88 L 392.5 402.88 L 396 404.63 L 399.5 402.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="381" y="411" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 431px; margin-left: 382px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Camera Video</div></div></div></foreignObject><text x="411" y="435" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="381" y="361" width="60" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 381px; margin-left: 382px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Human Audio</div></div></div></foreignObject><text x="411" y="385" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g></g></svg>