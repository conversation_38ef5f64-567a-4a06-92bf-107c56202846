default:
  logger:
    log_level: "INFO"
  service:
    host: "0.0.0.0"
    port: 8282
    cert_file: "ssl_certs/localhost.crt"
    cert_key: "ssl_certs/localhost.key"
  chat_engine:
    model_root: "models"
    handler_search_path:
      - "src/handlers"
    handler_configs:
      RtcClient:
        module: client/rtc_client/client_handler_rtc
        concurrent_limit: 1
      SileroVad:
        module: vad/silerovad/vad_handler_silero
        speaking_threshold: 0.5
        start_delay: 2048
        end_delay: 5000
        buffer_look_back: 5000
        speech_padding: 512
      SenseVoice:
        enabled: True
        module: asr/sensevoice/asr_handler_sensevoice
        model_name: "iic/SenseVoiceSmall"
      CosyVoice:
        enabled: True
        module: tts/bailian_tts/tts_handler_cosyvoice_bailian
        voice: "longxiaochun"
        model_name: "cosyvoice-v1"
        # api_key: "" # default=os.getenv("DASHSCOPE_API_KEY")
      LLM_Bailian:
        enabled: True
        module: llm/openai_compatible/llm_handler_openai_compatible
        model_name: "qwen-vl-plus"
        enable_video_input: True # ensure your llm support video input
        # model_name: "gemini-2.0-flash"
        system_prompt: "请你扮演一个 AI 助手，用简短的两三句对话来回答用户的问题，并在对话内容中加入合适的标点符号，不需要讨论标点符号相关的内容"
        api_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
        # api_url: 'http://127.0.0.1:11434/v1' # ollama
        # api_url: 'https://generativelanguage.googleapis.com/v1beta/openai/'
        # api_key: "" # default=os.getenv("DASHSCOPE_API_KEY")
      LiteAvatar:
        module: avatar/liteavatar/avatar_handler_liteavatar
        avatar_name: 20250408/sample_data
        fps: 25
        debug: false
        enable_fast_mode: false
        use_gpu: true
